import os
import re
import openpyxl
import json
from dotenv import load_dotenv
from trag import TRAG
from trag.types.document import Document
from backend.app.utils.tapd import tap_client
from backend.app.service.bug_evaluation_service import get_bug_from_dataset
load_dotenv()


class TRAGManager:
    """TRAG 文档管理类，负责文档的导入、搜索和清理"""

    def __init__(self, ns_code=os.getenv("TRAG_NAMESPACE"), coll_code=os.getenv("TRAG_CASE_COLL")):
        """初始化 TRAG 连接和 TAPD 工具"""
        self.rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
        self.namespace = self.rag.namespace(ns_code)
        self.collection = self.namespace.collection(coll_code)
        print(self.rag.info)

    def import_test_cases(self, file_path):
        """
        从 Excel 导入测试用例到 TRAG
        :param file_path: Excel 文件路径
        """
        headers = [
            "用例ID", "UUID", "用例标题", "用例目录", "用例描述", "用例负责人",
            "用例类型", "是否自动化", "等级", "前置条件", "步骤描述类型",
            "步骤描述", "预期结果", "评估工时", "标签", "关联需求",
            "关联的自动化用例", "执行次数", "创建人", "创建时间", "测试框架"
        ]
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        documents = []

        for row in sheet.iter_rows(min_row=2, values_only=True):
            row_data = self._parse_excel_row(row, headers)
            document = self._generate_case_document(row_data)
            documents.append(document)
            print(document.embedding_query)  # 打印简化版用例信息
            print(document.doc)  # 打印完整 JSON 格式

        workbook.close()
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个测试用例")

    def import_require(self, urls):
        """
        从 TAPD URL 列表导入需求文档到 TRAG
        :param urls: TAPD 需求 URL 列表
        """
        documents = []
        for url in urls:
            require = tap_client.get_story(url)
            if require:
                document = self._generate_require_document(require)
                documents.append(document)
                print(document.embedding_query)  # 打印简化版需求信息
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个需求")

    def import_bugs_from_dataset(self):
        """
        从数据库导入缺陷数据到TRAG
        """
        bugs = get_bug_from_dataset("20375472")
        documents = []
        for bug in bugs:
            tap_client.get_bug_all_pure_message(bug)
            document = self._generate_bug_document(bug)
            documents.append(document)
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个缺陷")


    def import_bugs(self, directory_path="bugs"):
        """
        从指定目录下的所有Excel文件导入BUG单到TRAG
        :param directory_path: Excel文件所在目录路径
        """
        import glob
        import os

        # 获取目录下所有的xlsx文件
        excel_files = glob.glob(os.path.join(directory_path, "*.xlsx"))
        
        total_documents = []
        
        for file_path in excel_files:
            print(f"正在处理文件: {file_path}")
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active
            
            # 从第一行获取动态headers
            headers = [cell.value for cell in sheet[1] if cell.value is not None]
            
            documents = []
            for row in sheet.iter_rows(min_row=2, values_only=False):
                row_data = self._parse_excel_row_solve_link(row, headers)
                if row_data:  # 确保行数据不为空
                    document = self._generate_bug_document(row_data)
                    documents.append(document)
                    print(document.embedding_query)  # 打印简化版用例信息
                    print(document.doc)  # 打印完整 JSON 格式

            workbook.close()
            total_documents.extend(documents)
            print(f"从文件 {file_path} 成功导入 {len(documents)} 个缺陷")
        
        if total_documents:
            self.collection.import_documents(total_documents)
            print(f"总共成功导入 {len(total_documents)} 个缺陷")
        else:
            print("没有找到任何缺陷数据")

    def search_test_cases(self, query, limit=10):
        """
        搜索测试用例
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :return: 搜索结果列表
        """
        return self.collection.search_documents(doc=query, limit=limit)

    def search_bugs(self, query, limit=10):
        """
        搜索缺陷
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :return: 搜索结果列表
        """
        return self.collection.search_documents(doc=query, limit=limit)
    def clean_documents(self):
        """清空 TRAG 文档库"""
        self.collection.clean_documents()

    def _parse_excel_row(self, row, headers):
        """
        解析 Excel 单行数据
        :param row: Excel 行数据
        :param headers: 表头列表
        :return: 解析后的字典
        """
        return {header: row[i] if row[i] is not None else "" for i, header in enumerate(headers)}


    def _parse_excel_row_solve_link(self, row, headers) -> dict:
        """
        解析Excel 单行数据
        :param row: Excel 行数据
        :param headers: 表头列表
        :return: 解析后的字典
        """
        result = {}
        for i, header in enumerate(headers):
            if row[i].value is not None:
                if row[i].hyperlink:
                    result[header] = row[i].value
                    result[header + "链接"] = row[i].hyperlink.target
                else:
                    result[header] = row[i].value
        return result

    def _generate_case_document(self, row_data):
        """
        生成测试用例文档对象
        :param row_data: 用例数据字典
        :return: Document 对象
        """
        simple_case = (
            f"用例目录: {row_data['用例目录']}\n"
            f"用例标题: {row_data['用例标题']}\n"
            f"步骤描述: {row_data['步骤描述']}\n"
            f"预期结果: {row_data['预期结果']}\n"
        )
        original_case = json.dumps(row_data, ensure_ascii=False, indent=2)
        return Document(
            id=row_data['用例ID'],
            embedding_query=simple_case,
            doc=original_case
        )

    def _generate_require_document(self, require):
        """
        生成需求文档对象
        :param require: 需求数据字典
        :return: Document 对象
        """
        require_str = json.dumps(require, ensure_ascii=False, indent=2)
        return Document(
            id=require['id'],
            embedding_query=require_str,
            doc=require_str
        )

    def _generate_bug_document(self, row_data):
        """
        生成缺陷文档对象
        :param row_data: 缺陷数据字典
        :return: Document 对象
        """
        simple_case = (
            f"缺陷标题：{row_data['缺陷标题']}\n"
            f"模块：{row_data.get('模块', '')}\n"
            f"关联需求：{row_data.get('关联需求','')}\n"
            f"状态：{row_data['状态']}\n"
            f"缺陷引入来源：{row_data.get('缺陷引入来源', '')}\n"
            f"优先级：{row_data.get('优先级', '')}\n"
            f"重现规律：{row_data.get('重现规律', '')}\n"
        )
        original_case = json.dumps(row_data, ensure_ascii=False, indent=2)
        return Document(
            id=row_data['缺陷ID'],
            embedding_query=simple_case,
            doc=original_case
        )

def create_ns(name):
    """创建命名空间"""
    rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
    return rag.create_namespace(name, description="")


def create_col(ns, name, description="", embedding_model="public-bge-m3", dimension=1024):
    """创建知识库集合"""
    return ns.create_collection(name=name, description=description, embedding_model=embedding_model, dimension=dimension)


if __name__ == "__main__":
    # 用例知识库全流程
    # 选择知识库
    manager = TRAGManager(coll_code=os.getenv("TRAG_CASE_COLL"))
    # # 导入
    # manager.import_test_cases("./data/test_cases.xlsx")
    #manager.import_bugs("../../data/bugs_896_优先级低.xlsx")
    # 检索
    #case_search = manager.search_test_cases(query="关于AIGC深度思考模块场景验证的历史用例有哪些", limit=10)
    #print(case_search)
    bugs_search = manager.search_bugs("健康管理助手模块有哪些历史BUG", limit=10)
    print(bugs_search)
    # # 需求知识库全流程
    # manager = TRAGManager(coll_code=os.getenv("TRAG_STORY_COLL"))
    # urls = [
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122077437?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122596893?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122425098?from_iteration_id=1020375472002148335",
    # ]
    # manager.import_require(urls)
    # require_search = manager.search_test_cases(query="关于咨询人的需求", limit=1)
    # print(require_search)