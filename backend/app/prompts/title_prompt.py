import json
from backend.app.config.config import JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID, YIYAO_SAAS_WORKSPACE_ID, MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID
import copy
def get_bug_title_three_element_need_prompt(bug_data, workspace_id) -> str:
    """三要素必要性判断 Prompt：固定要求“异常现象”，判断“操作位置”与“操作行为”是否必须体现于标题中（操作行为已体现则直接通过）"""
    return f"""
# 🎯背景 #
你是一位资深缺陷标题分析专家，擅长分析用户上传的缺陷表达的问题本质，并判断缺陷标题是否应该包含三要素，操作位置、操作行为、异常现象。

# 🧠 目标 & 思维链 #
请完成以下任务：
1. 理解缺陷的本质与触发流程。
2. 结合缺陷理解，按照顺序判断标题是否必须包含以下三要素。
3. 重新校验三要素的判断是否合理，并给出判断依据。
4. 输出判断结果（three-elements-result），并结合缺陷本质理解给出判断依据。


# 输入信息 #
1. 下面是整个原始缺陷的信息（包括原始标题），你可以基于此理解缺陷：
=====
{json.dumps(bug_data, ensure_ascii=False, indent=4)}
=====

# 📘三要素定义 #
1. 操作位置：用户在执行任何操作前最初所在的页面、模块或功能入口，是问题流程的起点，用于明确异常出现的上下文。
2. 操作行为：用户在操作位置上执行的动作，**通常是导致异常发生的触发动作**，但并非总是根因。
3. 异常现象：操作行为执行后，系统出现的不符合预期的表现，是问题最终暴露的形式。

   
# ✅ 判断原则 #
1. **操作位置**（默认必须体现，特殊情况可省略）：
    a. 可省略的情况：
        i. 若异常属于**浮窗/弹窗等全局组件**，可配置在任意页面触发，**与特定页面无关**，此类“位置无关异常”可省略。
        ii. 若异常属于**系统接口异常、数据错误、服务端问题等系统侧的直接异常**，**与页面无强绑定**，也可省略操作位置。
        iii. 若异常现象已能准确定位问题，且缺失位置不影响理解，可省略。
        iv. 若缺陷为“组件通用异常”（如浮窗按钮、弹窗文案等），其异常在多个页面都可能出现，无需绑定特定操作位置，此类异常可认定为**“位置无关异常”**，标题中可省略操作位置。
    b. 必须体现的情况：
       i. 如果存在多个操作行为，应该需保留操作位置。
    c. 特殊情况：
        i. 可使用泛指术语（如“业务侧”、“个人中心”），但控件级别不可视为操作位置。
        ii. 业务前缀不计入操作位置，例如“【腾讯健康】”。

2. **异常现象**（必须体现）：
   a. 缺陷核心，不可省略；
   b. 应准确描述暴露出的系统问题表现。

3. **操作行为**：
   a. 若标题中已体现操作行为，直接视为必要要素。
   b. 若未体现，则判断是否为异常暴露的**必要触发前提**：
        i. 如果只是自然浏览流程如“进入xx页面”、“用户查看xx内容”等用户常规操作内容，或缺陷异常本质为后台系统/接口波动导致的问题，则不需要操作行为。
        ii. 若异常现象已经可以表示问题的本质，则不需要操作行为。
    
# 注意事项 #
1. 若缺陷本质为某个页面或者某个功能异常，但并没有绝对的触发路径，那么只需异常现象即可。


#📚 示例参考 #（仅输出 JSON，不要其他解释说明）

# 示例1：位置无关异常，可省略操作位置 #
{{
    "thinking_process": "缺陷为“浮窗按钮文案长度未限制”，该浮窗组件在任意页面可配置，与具体页面无关，属位置无关异常；标题已体现异常现象，无需操作行为。",
    "needed_elements": ["异常现象"],
    "reason": "浮窗为全局组件，与页面无关，属位置无关异常；异常为配置导致，无需特定操作行为"
}}

# 示例2：操作行为必须体现 # 
{{
    "thinking_process": "缺陷为“搜索敏感词后，搜索结果页健康问问卡片样式异常”。标题中未提及搜索操作，异常是搜索敏感词操作后的结果，因此操作行为为必要前提。",
    "needed_elements": ["操作位置", "操作行为", "异常现象"],
    "reason": "异常依赖搜索操作触发，标题未体现操作行为，需补充"
}}
"""

def get_bug_title_quality_prompt_stage1(bug_data, three_elements_result, workspace_id) -> str:
    """标题质量评估第一阶段：基于三要素判断结果，对标题中的必要要素进行评分与反馈"""
    title_prefix_standard_dict = {
        JIANKANG_WORKSPACE_ID: "健康业务标题应以“【腾讯健康】”作为前缀",
        YIBAO_WORKSPACE_ID: "医保业务标题应以`【】`+ `需求完整标题（包括【产品需求】）`的格式作为前缀，其中'需求标题'可以从缺陷上下文的'需求名称'字段获取。例如需求标题为'【产品需求】展码页默认显示逻辑优化'，那么缺陷标题应为【【产品需求】展码页默认显示逻辑优化】作为前缀，不需要考虑存在重复【】的问题",
        YIYAO_SAAS_WORKSPACE_ID: "标题中使用【】括号表示业务、模块归属，不要进行修改；你需要优化标题的正文内容，使其符合规范。",
        MIYING_WORKSPACE_ID: "标题中使用【】括号表示业务、模块归属，不要进行修改；你需要优化标题的正文内容，使其符合规范。",
        DAOZHEN_WORKSPACE_ID: "标题中使用【】括号表示业务、模块归属，不要进行修改；你需要优化标题的正文内容，使其符合规范。"
    }
    title_prefix_standard = title_prefix_standard_dict.get(workspace_id, "默认标题前缀规范")
    bug_data_copy = copy.deepcopy(bug_data)
    title = bug_data_copy.pop("标题")
    title_work_position = ""
    if workspace_id == YIYAO_SAAS_WORKSPACE_ID:
        title_work_position = "医药业务的标题的操作位置可以参考标题前缀，如【会议】"
    else:
        title_work_position = "标题中的业务前缀（如“【腾讯健康】”）不计入操作位置。"
    return f"""
# 🎯 背景 #
你是一位经验丰富的缺陷标题质量评估专家，擅长分析用户上传的缺陷报告中的“标题”字段质量，并给出合理评分。

# 🧠 目标 & 思维链 #
你需要按照顺序完成以下任务：
1. ** 不结合任何输入信息例如（缺陷上下文、三要素判断结果）**，以标题为主，先单纯判断标题中是否包含三要素。
2. 分析第一步中缺少的三要素信息：
    a. 基于 `three_elements_result` 的三要素判断结果，判断缺少要素的是否必要：
        i. 若缺少的要素不在 `three_elements_result`的要求中，则可忽略，不需要再去分析其必要性。
        ii. 若缺少的要素在 `three_elements_result` 中被标记为必要，则需要结合缺陷上下文，确认缺少的三要素是否必要，并根据以下评分规则进行评分。
3. 表达评估：判断标题语言是否清晰、专业、易懂。
4. 打分输出：给出准确性与清晰性得分，并说明扣分原因。

# 📄 输入信息 #
1. 缺陷标题（原始标题）：
=====
{title}
=====

2. 缺陷上下文，你可以基于此理解缺陷本质：
=====
{json.dumps(bug_data_copy, ensure_ascii=False, indent=4)}
=====

3. 上一步中三要素判断结果（three-elements_result）：
=====
{json.dumps(three_elements_result, ensure_ascii=False, indent=4)}
=====

# ✅ 三要素说明 #
1. 操作位置：用户在执行任何操作前最初所在的页面、模块或功能入口，是问题流程的起点，用于明确异常出现的上下文。
2. 操作行为：用户在操作位置上执行的动作，**通常是导致异常暴露的触发动作**，但并非总是根因。
3. 异常现象：操作行为执行后，系统出现的不符合预期的表现，是问题最终暴露的形式。

# 📌 三要素判断原则 #
1. 操作位置需为页面/模块级/tab级，** 不需要太过详细，能明确出现异常的上下文即可 **，不得使用控件级表达（如“按钮”、“输入框”）。
2. {title_work_position}
3. 若标题中体现的操作行为没有完全表达缺陷的操作流程，应以标题中的操作行为为主。

# 三要素缺少是否必要规则 #
1. 若缺少操作位置：缺少不结合上下文无法理解缺陷是在哪里发生的异常，则视为缺少，若异常现象可以清楚地知道缺陷在哪里发生的异常，则视为不缺少。
2. 若缺少操作行为：若缺陷本质为某个页面或者某个功能异常，但并没有绝对核心的触发路径，只是普通步骤而已，则可不需要操作行为，但如果是特定步骤才能触发则需要操作行为。

# 评分规则 #
1. 标题前缀评分判断
    a. {title_prefix_standard}
    b. 若标题前缀不符合所属业务的规范，视为清晰性扣0.4分。

2. 标题内容评分：
    a. 准确性（满分1.0分）：
        i. 标题中若已体现该要素则直接通过不扣分，无需再判断其必要性。
        ii. 若标题中未体现该要素 → 再结合 three_elements_result 判断是否属于“必须体现”的必要信息**，若是，扣 0.4分。
    b. 清晰性（满分1.0分）：
        i. 轻度问题（如略显口语）扣 0.2。
        ii. 严重问题（如语义混乱、有语病、不好理解）扣 0.4。

# 注意事项 #
1. 若缺陷本质为某个页面或者某个功能异常，但并没有绝对的触发路径，那么只需异常现象即可。
1. 若标题中缺少关键信息，但结合缺陷上下文可以理解，也视为不通过。
2. 若某个维度未扣分，不要在 `feedback` 中输出该维度。
3. `feedback` 中仅包含实际发生扣分的维度及扣分原因，若该纬度为1.0分则不包含。
4. `dimension_scores` 中每个维度仍需输出分数（float）。

# 📤 输出示例（仅输出 JSON，禁止任何解释性内容）#
{{
    "thinking_process": "详细中文思维链过程。先判断标题中三要素是否体现，缺失项再结合三要素判断结果评估其必要性，最后进行语言表达评估与打分",
    "dimension_scores": {{
        "准确性": float,
        "清晰性": float
    }},
    "feedback": "准确性 -X：原因；清晰性 -X：原因"
}}
"""



def get_bug_title_quality_prompt_stage2(bug_data, stage1_result, workspace_id) -> str:
    """标题质量评估第二阶段：基于第一步三要素和第一阶段评分结果，生成高质量建议标题"""
    bug_data_copy = copy.deepcopy(bug_data)
    title = bug_data_copy.pop("标题", "")
    title_prefix_standard_dict = {
        JIANKANG_WORKSPACE_ID: "健康业务标题应以“【腾讯健康】”作为前缀",
        YIBAO_WORKSPACE_ID: "医保业务标题应以【】加需求标题的格式作为前缀，其中'需求标题'可以从缺陷上下文的'需求名称'字段获取。例如需求标题为'【产品需求】展码页默认显示逻辑优化'，那么缺陷标题应为【【产品需求】展码页默认显示逻辑优化】作为前缀。",
        YIYAO_SAAS_WORKSPACE_ID: "标题前缀中使用【】括号表示业务、模块归属，不要修改标题前缀；你需要优化标题的正文内容，使其符合规范。",
        MIYING_WORKSPACE_ID: "标题前缀中使用【】括号表示业务、模块归属，不要修改标题前缀；你需要优化标题的正文内容，使其符合规范。",
        DAOZHEN_WORKSPACE_ID: "标题前缀中使用【】括号表示业务、模块归属，不要修改标题前缀；你需要优化标题的正文内容，使其符合规范。"
    }
    title_prefix_standard = title_prefix_standard_dict.get(workspace_id, "默认标题前缀规范")
    return f"""
# 🎯 背景 #
你是一位资深缺陷标题优化专家，擅长基于缺陷内容与结构化分析结果，生成高质量的缺陷标题。

# 🧠 任务目标 & 思维链 #
请完成以下任务：
1. 分析理解缺陷的本质，明确缺陷暴露的问题发生的根因，理解缺陷想表达的本质。
2. 基于你对缺陷的理解，分析第一阶段标题质量评分结果是否合理
3. 结合缺陷上下文以及你对缺陷的理解和第一阶段评分结果，生成一个高质量的建议标题。

# 📄 输入信息 #
1. 缺陷标题（原始标题）：
=====
{title}
=====

2. 缺陷详情（原始缺陷内容）：
=====
{json.dumps(bug_data_copy, ensure_ascii=False, indent=4)}
=====

3. 第一阶段标题质量评分结果：
=====
{json.dumps(stage1_result, ensure_ascii=False, indent=4)}
=====

# ✅ 三要素说明 #
1. 操作位置：用户在执行任何操作前最初所在的页面、模块或功能入口，是问题流程的起点，用于明确异常出现的上下文。
2. 操作行为：用户在操作位置上执行的动作，**通常是导致异常暴露的触发动作**，但并非总是根因。
3. 异常现象：操作行为执行后，系统出现的不符合预期的表现，是问题最终暴露的形式。

# 📌 标题生成规则 #
1. 内容要求：
    a. {title_prefix_standard}
    b. 不得添加缺陷内容中未提及的额外信息，避免凭空扩展或主观臆测。
    c. 操作行为和异常现象为多个时，应概括表达，不必列举全部细节
    d. 若根据第一步评分结果需要修复操作行为或异常现象：
        i. 若存在多个操作行为或异常现象时，需要判断哪个才是缺陷的核心本质：
            1. 若标题中存在操作行为或异常现象，则应当认为原始标题体现的即为缺陷核心本质。
            2. 若标题中未体现操作行为或异常现象，则应当合理分析缺陷本质并给出建议。
    e. ** 若标题中含有关键信息，在生成建议时不得删除原有的关键信息 **。

2. 表达规范：
    a. 保持标题简洁、专业、无歧义，语言表达清晰，避免口语化或缩略语。
    b. 操作行为和异常现象为多个时，应概括表达，不必列举全部细节：
        i. 若多个异常现象中有**明显核心问题**，应突出核心；
        ii. 若无核心，应笼统描述整个集合。
    c. 若存在“模块归属 + 控件位置”的描述（如“健康问问输入框上方”），应保留模块信息，避免仅写“输入框上方”造成歧义。

# 特别说明 #：
    1. 操作位置需为页面/模块级/tab级，不需要太过详细，能明确出现异常的上下文即可，不得使用控件级表达（如“按钮”、“输入框”）。
    2. 多步骤缺陷中，操作行为应**概括关键步骤**，非列出全部流程；
    3. 异常现象为多个时，应**概括整体或突出核心问题**，避免细节堆砌。

# 📤 输出格式（仅输出 JSON，使用双引号，禁止其他说明）#
{{
    "thinking_process": "1. 首先理解缺陷本质，明确缺陷想表达的重点。2. 分析第一阶段评分结果，判断是否合理。3. 结合缺陷上下文和评分结果，生成高质量建议标题。",
    "suggest": "建议标题"
}}
"""

