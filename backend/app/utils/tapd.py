import json

from bs4 import BeautifulSoup
import html2text
from markdownify import markdownify as md
from typing import List, Dict
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID, DESCRIPTION_MAX_LENGTH
from dataclasses import dataclass, field
from typing import List, Dict
import requests
from requests.exceptions import RequestException
from html2text import HTML2Text
import re

class HTMLProcessor:
    def __init__(self, excluded_tags=None, markdown_config=None):
        self.excluded_tags = excluded_tags or ['script', 'style', 'nav']
        self.markdown_config = markdown_config or {'body_width': 0}

    def clean_html(self, html):
        """清理指定标签的HTML文档"""
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup(self.excluded_tags):
            tag.decompose()
        return str(soup)

    def convert_to_markdown(self, html):
        """转换为Markdown格式"""
        converter = html2text.HTML2Text()
        converter.body_width = self.markdown_config.get('body_width', 0)
        return converter.handle(self.clean_html(html))


def extract_video_links(html: str) -> str:
    def replacer(match):
        src = match.group(1)
        return f"\n[🎥 视频预览]({src})\n"

    # 提取 <video src="..."> 或 <source src="...">
    html = re.sub(r'<video[^>]+src="([^"]+)"[^>]*>.*?</video>', replacer, html, flags=re.DOTALL)
    html = re.sub(r'<source[^>]+src="([^"]+)"[^>]*>', replacer, html)
    return html

def md_with_fix(html: str) -> str:
    html = extract_video_links(html)  # 提取视频链接为 Markdown 格式

    html = re.sub(r'</div>', '</div>\n', html)
    html = re.sub(r'<br\s*/?>', '\n', html)
    html = re.sub(r'</p>', '</p>\n', html)

    h = HTML2Text()
    h.body_width = 0
    h.ignore_images = False
    h.ignore_links = False
    h.ignore_emphasis = True  # 关键：忽略 <i>/<em> 斜体转换
    return h.handle(html).strip()

def bug_html2md(bugs_data: dict, *args):
    for arg in args:
        if isinstance(arg, str) and arg in bugs_data and isinstance(bugs_data[arg], str):
            bugs_data[arg] = md_with_fix(bugs_data[arg])

class TAPDClient:
    def __init__(self, base_url='http://apiv2.tapd.woa.com', auth_token=None, workspace_id=None):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Basic {auth_token}' if auth_token else ''
        }
        self.fields_info = self.get_fields_info(workspace_id)
        self.fields_label = self.get_fields_lable(workspace_id)
        self.workspace_id = workspace_id

    def get_story_name(self, workspace_id, story_id):
        """获取 tapd需求名称"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'name'
        }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('name')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_story(self, workspace_id, story_id):
        """获取 tapd需求全部信息"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'id,name,status,owner,description'
        }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_story_description(self, workspace_id, story_id):
        """获取 tapd需求详情"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'description'
        }

        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('description')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_image(self, workspace_id, image_path):
        """
        获取TAPD图片
        参数:
            workspace_id: 工作区ID
            image_path: 图片路径(如/tfl/captures/2023-07/tapd_10104801_base64_1689686020_146.png)
        返回:
            requests.Response对象
        """
        params = {
            'workspace_id': workspace_id,
            'image_path': image_path
        }

        try:
            response = requests.get(
                f"{self.base_url}/files/get_image",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            response = response.json()
            download_url = response.get('data', {}).get('Attachment', {}).get('download_url', "")
            return download_url
        except RequestException as e:
            raise TAPDError(f"获取图片失败: {str(e)}") from e
        
    def get_stories_by_ids(self, workspace_id, ids):

        """
        通过workspace_id和ids获取tapd需求详情
        """
        params = {
            'workspace_id': workspace_id,
            "id": ",".join(map(str, ids))
            }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            #[{'Story': {...}}, {'Story': {...}}]
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e
        

    def get_bug_by_id(self, workspace_id, bug_id):
        """
        通过workspace_id，bug_id获取tapd缺项详情
        """
        params = {
            'workspace_id': workspace_id,
            'id': bug_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            if data['data'] == []:
                return {}
            bug_data = data.get('data', [{}])[0].get('Bug', {})
            #filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return bug_data
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_custom_fields_settings(self, workspace_id) -> List[Dict]:

        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/custom_fields_settings",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            custom_fields_config = data.get('data', [{}])
            # filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return custom_fields_config
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def update_bug(self, workspace_id, bug_id, description):
        data = {
            'workspace_id': workspace_id,
            'id': bug_id,
            'description': description
        }
        try:
            response = requests.post(
                f"{self.base_url}/bugs",
                headers=self.headers,
                data=data,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def get_fields_lable(self, workspace_id) -> List[Dict]:
        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_fields_lable",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            custom_fields_config = data.get('data', {})
            # filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return custom_fields_config
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def replace_custom_fields(self, bug_data: dict, custom_fields_config):
        # 生成字段映射字典：{custom_field -> name}
        field_mapping = {
            cfg['CustomFieldConfig']['custom_field']: cfg['CustomFieldConfig']['name']
            for cfg in custom_fields_config
            if cfg['CustomFieldConfig']['enabled'] == "1"  # 只处理启用状态的配置
        }

        # 创建新字典（保留原始非自定义字段，替换已配置的自定义字段）
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in field_mapping:
                # 替换自定义字段名称
                new_key = field_mapping[key]
            else:
                # 保留非自定义字段
                new_key = key

            # 避免键冲突（如果多个custom_field映射到同一name）
            if new_key in new_bug_data:
                if isinstance(new_bug_data[new_key], list):
                    new_bug_data[new_key].append(value)
                else:
                    new_bug_data[new_key] = [new_bug_data[new_key], value]
            else:
                new_bug_data[new_key] = value

        return new_bug_data
    
    def get_bugs(self, workspace_id, limit=200, page=1) -> List[Dict]:
        params = {
            'workspace_id': workspace_id,
            'limit': limit,
            'page': page,
            'order': 'created desc',
        }
        try:
            response = requests.get(f"{self.base_url}/bugs", headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_bugs_by_time_range(self, workspace_id, start_time=None, end_time=None, limit=200, page=1) -> List[Dict]:
        """
        根据时间范围获取缺陷列表

        Args:
            workspace_id: 工作区ID
            start_time: 开始时间，格式：'2024-01-01 00:00:00' 或 '2024-01-01'
            end_time: 结束时间，格式：'2024-01-01 23:59:59' 或 '2024-01-01'
            limit: 每页数量，默认200，最大200
            page: 页码，从1开始

        Returns:
            缺陷数据列表
        """
        params = {
            'workspace_id': workspace_id,
            'limit': limit,
            'page': page,
            'order': 'created desc',
        }

        created_filters = []

        if start_time:
            if len(start_time) == 10:
                start_time = f"{start_time} 00:00:00"
            created_filters.append(f"{start_time}")

        if end_time:
            if len(end_time) == 10:
                end_time = f"{end_time} 23:59:59"
            created_filters.append(f"{end_time}")

        if created_filters:
            params['created'] = "~".join(created_filters)

        try:
            response = requests.get(f"{self.base_url}/bugs", headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def get_all_bugs_by_time_range(self, workspace_id, start_time=None, end_time=None, max_bugs=None) -> List[Dict]:
        """
        获取指定时间范围内的所有缺陷（自动分页）

        Args:
            workspace_id: 工作区ID
            start_time: 开始时间，格式：'2024-01-01 00:00:00' 或 '2024-01-01'
            end_time: 结束时间，格式：'2024-01-01 23:59:59' 或 '2024-01-01'
            max_bugs: 最大获取数量，None表示获取所有

        Returns:
            所有缺陷数据列表
        """
        all_bugs = []
        page = 1

        while True:
            # 获取当前页数据
            bugs_page = self.get_bugs_by_time_range(
                workspace_id=workspace_id,
                start_time=start_time,
                end_time=end_time,
                limit=200,
                page=page
            )

            if not bugs_page:
                break

            all_bugs.extend(bugs_page)

            # 检查是否达到最大数量限制
            if max_bugs and len(all_bugs) >= max_bugs:
                all_bugs = all_bugs[:max_bugs]
                break

            # 如果返回的数据少于200条，说明已经是最后一页
            if len(bugs_page) < 200:
                break

            page += 1

        return all_bugs
        
    

    def replace_labels_fields(self, bug_data: dict, labels_fields_config):
        # 生成字段映射字典：{labels -> name}
        # 创建新字典（保留原始非自定义字段，替换已配置的自定义字段）
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in labels_fields_config:
                # 替换自定义字段名称
                new_key = labels_fields_config[key]
            else:
                # 保留非自定义字段
                new_key = key

            # 避免键冲突（如果多个custom_field映射到同一name）
            if new_key in new_bug_data:
                if isinstance(new_bug_data[new_key], list):
                    new_bug_data[new_key].append(value)
                else:
                    if new_bug_data[new_key] == "":
                        new_bug_data[new_key] = value
            else:
                new_bug_data[new_key] = value

        return new_bug_data

    def get_fields_info(self, workspace_id) -> Dict:
        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_fields_info",
                headers=self.headers,
                params=params,
                timeout=10,
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            fields_info = data.get('data', {})
            return fields_info
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def delete_custom_fields(self, new_bug_data):
        # 生成待删除键列表（避免遍历时修改字典结构）
        custom_keys = [k for k in new_bug_data if k.startswith('custom')]

        # 遍历删除（兼容Python3.7+的有序字典特性）
        for key in custom_keys:
            del new_bug_data[key]  # 直接删除不需要返回值的场景[2,6](@ref)
        return new_bug_data
    
    def extract_fields_value(self, bug_data, fields_label):
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in fields_label:
                if "options" in fields_label[key] and isinstance(fields_label[key]["options"], dict):
                    chinese_value = fields_label[key]["options"].get(value, "")
                    new_bug_data[key] = chinese_value
                else:
                    new_bug_data[key] = value
        return new_bug_data
    
    def get_story_info_for_bug(self, workspace_id, bug_data):
        # 获取相关联的需求
        bug_id = bug_data.get("ID", "")
        story_id_list = self.get_bug_related_story(workspace_id, [bug_id])
        # 获取需求名称
        story_name = ""
        story_url = ""
        if story_id_list != [{}]:
            for story_bugs in story_id_list:
                if story_bugs['bug_id'] == bug_id:
                    story_id = story_bugs['story_id']
                    story_name = self.get_story_name(workspace_id, story_id)
                    # 需求链接
                    story_url = f"https://tapd.woa.com/tapd_fe/{str(workspace_id)}/story/detail/{str(story_id)}"
        bug_data["需求链接"] = story_url
        bug_data["需求名称"] = story_name
        return bug_data
    
    def process_bug_field(self, bug_data: dict, workspace_id):
        if bug_data is None or bug_data == {}:
            return {}
        del bug_data["priority"]
        chinese_value_bug_data = self.extract_fields_value(bug_data, self.fields_info)
        # 进行替换
        new_bug_data = self.replace_labels_fields(chinese_value_bug_data, self.fields_label)
        # 删除多余的自定义字段
        new_bug_data_no_custom = self.delete_custom_fields(new_bug_data)
        return new_bug_data_no_custom
    
    def process_bug_data(self, bug_data: dict, workspace_id) -> dict:
        """
        待完善
        """
        new_bug_data_no_custom = self.process_bug_field(bug_data, workspace_id)
        self.get_story_info_for_bug(workspace_id, new_bug_data_no_custom)
        # 处理html转为md
        print("html: " + new_bug_data_no_custom["详细描述"])
        bug_html2md(new_bug_data_no_custom, "详细描述")
        print("md: " + new_bug_data_no_custom["详细描述"])
        new_bug_data_no_custom["详细描述"] = new_bug_data_no_custom["详细描述"][:DESCRIPTION_MAX_LENGTH]
        new_bug_data_no_custom["详细描述"] = new_bug_data_no_custom["详细描述"].replace("*", "")
        #new_bug_data_no_custom["详细描述"] = new_bug_data_no_custom["详细描述"].replace("_", "")
        return new_bug_data_no_custom
    
    def get_comments(self, workspace_id, bug_id: list):
        params = {
            'workspace_id': workspace_id,
            'entry_type': 'bug|bug_remark',
            'limit': 100,
            'entry_id': bug_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/comments",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e    

        # 获取原始bug数据
    def get_bug_all_pure_message(self, workspace_id, bug_id):
        # 获取原始bug数据
        bug_data = self.get_bug_by_id(workspace_id, bug_id)
        return self.process_bug_data(bug_data, workspace_id)

    def get_bug_related_story(self, workspace_id, bug_ids: list) -> list[dict]:
        """
        通过workspace_id和bug_id获取bug想关联的story_id
        """
        params = {
            'workspace_id': workspace_id,
            'bug_id': ",".join(map(str, bug_ids)),        
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_related_stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_id_list: list = data.get('data', [{}])
            return story_id_list 
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


# exceptions.py
class TAPDError(Exception):
    """自定义API异常"""
    pass


class HTMLConversionError(Exception):
    """HTML转换异常"""
    pass



@dataclass
class AppConfig:
    """应用配置"""
    auth_token: str = 'UHJvY2Vzc01hbmFnZVBsYXRmcm9tOjFFMjE2NEJCLUUzRkQtQTRBNi1FNUFDLUI1MDMxMDNDQzg3MQ=='
    excluded_tags: List[str] = field(default_factory=lambda: ['script', 'style', 'nav', 'footer'])
    markdown_config: Dict[str, bool] = field(default_factory=lambda: {'body_width': 0, 'wrap_links': True})
    api_timeout: int = 15

config = AppConfig()
tap_client = TAPDClient(auth_token=config.auth_token, workspace_id=BUG_EVALUATION_WORKSPACE_ID)
if __name__ == '__main__':
    test = "update_bug"
    if test == "get_bug_by_id":
        print(json.dumps(tap_client.get_bug_by_id(workspace_id=20375472, bug_id=1020375472142465367), indent=4, ensure_ascii=False))
    elif test == "get_image":
        print(tap_client.get_image(20375472, "/tfl/captures/2025-05/tapd_20375472_base64_1747364325_203.png"))
    elif test == "get_bug_related_story":
        print(tap_client.get_bug_related_story(20375472, [1020375472142465367]))
    elif test == "get_custom_fields_settings":
        print(tap_client.get_custom_fields_settings(workspace_id=20375472))
    elif test == "get_bug_all_pure_message":
        print(tap_client.get_bug_all_pure_message(workspace_id=20375472, bug_id=1020375472142465367))
    elif test == "get_stories_by_ids":
        print(tap_client.get_stories_by_ids(workspace_id=20375472, ids=[1020375472124209140, 1020375472124206566]))
    elif test == "get_comments":
        print(tap_client.get_comments(workspace_id=20375472, bug_id=[1020375472142176342, 1020375472142052015]))
    elif test == "update_bug":
        import re
        import markdown
        def convert_custom_video(md_text: str) -> str:
            # 把 !video[alt](url) 替换成 <video controls src="url">alt</video>
            video_pattern = re.compile(r'!video\[(.*?)\]\((.*?)\)')
            md_text = video_pattern.sub(r'<video controls src="\2">\1</video>', md_text)
            return md_text

        md_text = '''
**【条件】**  
体验版  
**【机型】**  
华为mate60，操作系统版本未提供

**【步骤】**  
1. 打开App->进入首页->点击城市切换按钮->选择不同城市  
2. 观察每次切换城市时的弹窗行为  
3. 记录弹窗出现的频率和内容

**【预期结果】**  
切换城市时不应频繁弹出运营弹窗，或弹窗出现频率应符合设计规范

**【实际结果】**  
每次切城市都会弹出运营弹窗，影响用户体验

!video[video](https://tapd.woa.com/20375472/attachments/preview_attachments/1020375472536206842/bug_description)
'''

        processed_md = convert_custom_video(md_text)
        html = markdown.markdown(processed_md)
        print(html)
        print(tap_client.update_bug(workspace_id=20375472, bug_id=1020375472143567123, description=html))