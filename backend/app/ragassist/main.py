from backend.app.scripts.trag.trag_manager import TRAGManager
from backend.app.config.config import TRAG_CASE_COLL
from backend.app.service.bug_evaluation_service import get_bug_from_dataset
from backend.app.utils.logger_util import logger
def main():
    manager = TRAGManager(TRAG_CASE_COLL)
    bug_datas = get_bug_from_dataset(workspace_id='20375472')
    logger.info(f"获取到的BUG数据：{bug_datas}")
    #for bug_data in bug_datas:


if __name__ == '__main__':
    main()


